export enum ValueAggregation {
  ValueSumAggregator = 'valueSumAggregator',
  ValueCountAggregator = 'valueCountAggregator', // Just count utrvs
  ValueConcatenateAggregator = 'valueConcatenateAggregator',
  ValueAverageAggregator = 'valueAverageAggregator',
  ValueListCountAggregator = 'valueListCountAggregator',
  TextCountAggregator = 'textCountAggregator',
  NumericValueListSumAggregator = 'numericValueListSumAggregator',
  NumericValueListAverageAggregator = 'numericValueListAverageAggregator',
  TableColumnAggregator = 'tableAggregator',
  TableConcatenationAggregator = 'tableConcatenationAggregator',
  LatestAggregator = 'latestAggregator',
  EmptyAggregator = 'emptyAggregator',
  TableRowGroupAggregator = 'tableRowGroupAggregator',
  TextAISummary = 'textAISummary',
}

export enum ColumnValueAggregation {
  ColumnSumAggregator = 'columnSumAggregator',
  ColumnAverageAggregator = 'columnAverageAggregator',
  ColumnLatestAggregator = 'columnLatestAggregator',
  ColumnMaxAggregator = 'columnMaxAggregator',
  ColumnEmptyAggregator = 'columnEmptyAggregator',
  ColumnConcatenateAggregator = 'columnConcatenateAggregator',
  ColumnPostAggregationCalculation = 'columnPostAggregationCalculation',
  ColumnWeightedAverageAggregator = 'columnWeightedAverageAggregator',
  ColumnAISummary = 'columnAISummary',
}

// Aggregation mode enum for better organization
export enum AggregationMode {
  Children = 'children', // Used for tree aggregations up to parent
  Combined = 'combined', // Used for combined reports (e.g. monthly reports to a combined yearly)
}

export const aggregationTypes: { [key in ValueAggregation]: string } = {
  latestAggregator: 'LATEST VALUE: Most recently submitted value',
  valueSumAggregator: 'SUM: Add up all values',
  valueAverageAggregator: 'AVERAGE: Simple average',
  valueConcatenateAggregator: 'CONCAT: Concat submitted values',
  valueListCountAggregator: 'TEXT VALUE LIST COUNT: Create a list of possible options with their respective counts',
  numericValueListAverageAggregator: 'VALUE LIST AVERAGE: Simple average from each list option',
  textCountAggregator: 'TEXT LIST COUNT: Create a list of possible text options with their respective counts',
  numericValueListSumAggregator: 'VALUE LIST SUM: Add up each list option',
  tableAggregator:
    'TABLE: Aggregation will respect the aggregation method specified in the configuration for the column',
  tableRowGroupAggregator:
    'TABLE ROW GROUP: Aggregation will group by columns, and then respect the aggregation method specified in the configuration for the column',
  tableConcatenationAggregator: 'TABLE CONCATENATION: Appends all rows to table',
  emptyAggregator: 'IGNORE: Do not aggregate',
  valueCountAggregator: 'COUNT: Count of submitted instance for this input',
  textAISummary: "AI SUMMARY: Generate AI-powered summary of aggregated text values",
};

export const columnAggregationTypes: { [key in ColumnValueAggregation]: string } = {
  columnSumAggregator: 'SUM: Add up all column values',
  columnAverageAggregator: 'AVERAGE: Simple average of column values',
  columnLatestAggregator: 'LATEST: Most recently submitted column value',
  columnMaxAggregator: 'MAX: Maximum column value',
  columnEmptyAggregator: 'IGNORE: Do not aggregate column',
  columnConcatenateAggregator: 'CONCAT: Concatenate column values',
  columnPostAggregationCalculation: 'CALCULATE: Recalculate after aggregation',
  columnWeightedAverageAggregator: 'WEIGHTED AVERAGE: Weighted average using weight formula',
  columnAISummary: "AI SUMMARY: Generate AI-powered summary of aggregated column values",
};

export enum UtrValueType {
  Number = 'number',
  Sample = 'sample', // @deprecated
  Percentage = 'percentage',
  Text = 'text',
  Date = 'date',
  ValueListMulti = 'valueListMulti', // MultiSelect
  ValueList = 'valueList', // Single Select
  NumericValueList = 'numericValueList', // Numeric Multi Checkbox
  TextValueList = 'textValueList', // Multi Checkbox
  Table = 'table',
}

export enum ColumnType {
  Number = 'number',
  Text = 'text',
  Date = 'date',
  Percentage = 'percentage',
  ValueList = 'valueList',
  ValueListMulti = 'valueListMulti',
}

export type ValueAggregationCompatibilityInterface = {
  [key in UtrValueType]: {
    default: ValueAggregation;
    compatible: ValueAggregation[];
  };
};
export type ColumnAggregationCompatibilityInterface = {
  [key in ColumnType]: {
    default: ColumnValueAggregation;
    compatible: ColumnValueAggregation[];
  };
};

export const defaultCompatible = [
  ValueAggregation.LatestAggregator,
  ValueAggregation.EmptyAggregator,
  ValueAggregation.ValueCountAggregator,
];

export const ValueAggregationSiblingsCompatibility: ValueAggregationCompatibilityInterface = {
  [UtrValueType.Number]: {
    default: ValueAggregation.ValueSumAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueSumAggregator, ValueAggregation.ValueAverageAggregator],
  },
  [UtrValueType.Percentage]: {
    default: ValueAggregation.ValueAverageAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueSumAggregator, ValueAggregation.ValueAverageAggregator],
  },
  [UtrValueType.Table]: {
    default: ValueAggregation.TableColumnAggregator,
    compatible: [
      ...defaultCompatible,
      ValueAggregation.TableColumnAggregator,
      ValueAggregation.TableConcatenationAggregator,
      ValueAggregation.TableRowGroupAggregator,
    ],
  },
  [UtrValueType.NumericValueList]: {
    default: ValueAggregation.NumericValueListSumAggregator,
    compatible: [
      ...defaultCompatible,
      ValueAggregation.NumericValueListSumAggregator,
      ValueAggregation.NumericValueListAverageAggregator,
    ],
  },
  [UtrValueType.ValueListMulti]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueListCountAggregator],
  },
  [UtrValueType.ValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.ValueListCountAggregator],
  },
  [UtrValueType.Text]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [
      ...defaultCompatible,
      ValueAggregation.TextCountAggregator,
      ValueAggregation.ValueConcatenateAggregator,
      ValueAggregation.TextAISummary,
    ],
  },
  [UtrValueType.Date]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.TextCountAggregator],
  },

  // Unhandled
  [UtrValueType.Sample]: {
    default: ValueAggregation.LatestAggregator,
    compatible: defaultCompatible,
  },
  [UtrValueType.TextValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [...defaultCompatible, ValueAggregation.TextCountAggregator],
  },
};

export const ValueAggregationChildrenCompatibility: ValueAggregationCompatibilityInterface = {
  [UtrValueType.Number]: {
    default: ValueAggregation.ValueSumAggregator,
    compatible: [ValueAggregation.ValueSumAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.Percentage]: {
    default: ValueAggregation.ValueAverageAggregator,
    compatible: [ValueAggregation.ValueAverageAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.Table]: {
    default: ValueAggregation.TableColumnAggregator,
    compatible: [
      ValueAggregation.TableColumnAggregator,
      ValueAggregation.TableRowGroupAggregator,
      ValueAggregation.EmptyAggregator,
    ],
  },
  [UtrValueType.NumericValueList]: {
    default: ValueAggregation.NumericValueListSumAggregator,
    compatible: [ValueAggregation.NumericValueListSumAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.ValueListMulti]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.ValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator],
  },
  [UtrValueType.Text]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [
      ValueAggregation.LatestAggregator,
      ValueAggregation.EmptyAggregator,
      ValueAggregation.ValueConcatenateAggregator,
      ValueAggregation.TextAISummary,
    ],
  },
  [UtrValueType.Date]: {
    default: ValueAggregation.LatestAggregator,
    compatible: [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator],
  },

  // Unhandled
  [UtrValueType.Sample]: {
    default: ValueAggregation.LatestAggregator,
    compatible: defaultCompatible,
  },
  [UtrValueType.TextValueList]: {
    default: ValueAggregation.LatestAggregator,
    compatible: defaultCompatible,
  },
};

export const ColumnAggregationCompatibility: ColumnAggregationCompatibilityInterface = {
  [ColumnType.Number]: {
    default: ColumnValueAggregation.ColumnSumAggregator,
    compatible: [
      ColumnValueAggregation.ColumnSumAggregator,
      ColumnValueAggregation.ColumnAverageAggregator,
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnMaxAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator,
      ColumnValueAggregation.ColumnPostAggregationCalculation,
      ColumnValueAggregation.ColumnWeightedAverageAggregator,
    ],
  },
  [ColumnType.Percentage]: {
    default: ColumnValueAggregation.ColumnAverageAggregator,
    compatible: [
      ColumnValueAggregation.ColumnAverageAggregator,
      ColumnValueAggregation.ColumnSumAggregator,
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnMaxAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator,
      ColumnValueAggregation.ColumnPostAggregationCalculation,
      ColumnValueAggregation.ColumnWeightedAverageAggregator,
    ],
  },
  [ColumnType.Text]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator,
      ColumnValueAggregation.ColumnConcatenateAggregator,
      ColumnValueAggregation.ColumnAISummary,
    ],
  },
  [ColumnType.Date]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnLatestAggregator, ColumnValueAggregation.ColumnEmptyAggregator],
  },
  [ColumnType.ValueListMulti]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnLatestAggregator, ColumnValueAggregation.ColumnEmptyAggregator],
  },
  [ColumnType.ValueList]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [ColumnValueAggregation.ColumnLatestAggregator, ColumnValueAggregation.ColumnEmptyAggregator],
  },
};
